{"class_name": "<PERSON><PERSON>", "rules": [{"id": "TB_001", "category": "wall_thickness_constraint", "title": "Minimum Wall Thickness", "description": "Minimum wall thickness for structural integrity", "rule": "Wall thickness >= 0.5mm for small tubes, >= 1.0mm for large tubes (outer dimension > 50mm)", "severity": "error", "validation_code": "thickness >= 0.5 if max(outer_width, outer_height) <= 50 else thickness >= 1.0", "error_message": "ERROR: Wall thickness is too thin, may cause structural failure.", "parameters": ["thickness", "outer_width", "outer_height"]}, {"id": "TB_002", "category": "wall_thickness_constraint", "title": "Maximum Wall Thickness Ratio", "description": "Wall thickness should not exceed 50% of the smallest outer dimension", "rule": "Wall thickness <= 0.5 * min(outer_width, outer_height)", "severity": "warning", "validation_code": "thickness <= 0.5 * min(outer_width, outer_height)", "error_message": "WARNING: Wall thickness is too large relative to tube dimensions.", "parameters": ["thickness", "outer_width", "outer_height"]}, {"id": "TB_003", "category": "geometry_validation", "title": "Inner Dimension Validation", "description": "Inner dimensions must be positive after accounting for wall thickness", "rule": "Inner width and height must be > 0", "severity": "error", "validation_code": "(outer_width - 2 * thickness) > 0 and (outer_height - 2 * thickness) > 0", "error_message": "ERROR: Wall thickness is too large, resulting in negative inner dimensions.", "parameters": ["thickness", "outer_width", "outer_height"]}, {"id": "TB_004", "category": "fillet_constraint", "title": "Outer Fillet Radius Limit", "description": "Outer fillet radius should not exceed wall thickness", "rule": "Outer fillet radius <= wall thickness", "severity": "warning", "validation_code": "outer_fillet_radius <= thickness", "error_message": "WARNING: Outer fillet radius is too large relative to wall thickness.", "parameters": ["outer_fillet_radius", "thickness"]}, {"id": "TB_005", "category": "fillet_constraint", "title": "Inner Fillet Radius Limit", "description": "Inner fillet radius should be smaller than outer fillet radius", "rule": "Inner fillet radius <= outer fillet radius", "severity": "warning", "validation_code": "inner_fillet_radius <= outer_fillet_radius", "error_message": "WARNING: Inner fillet radius should not exceed outer fillet radius.", "parameters": ["inner_fillet_radius", "outer_fillet_radius"]}, {"id": "TB_006", "category": "dimension_ratio", "title": "Aspect Ratio Limit", "description": "Length to cross-section ratio for manufacturability", "rule": "Length/max(outer_width, outer_height) should not exceed 100:1", "severity": "warning", "validation_code": "length / max(outer_width, outer_height) <= 100", "error_message": "WARNING: Tube is too long relative to cross-section, may cause deflection.", "parameters": ["length", "outer_width", "outer_height"]}, {"id": "TB_007", "category": "manufacturing_constraint", "title": "Minimum Cross-Section Dimension", "description": "Minimum outer dimension for manufacturing feasibility", "rule": "Outer width and height >= 2.0mm", "severity": "warning", "validation_code": "outer_width >= 2.0 and outer_height >= 2.0", "error_message": "WARNING: Tube cross-section is too small, may be difficult to manufacture.", "parameters": ["outer_width", "outer_height"]}, {"id": "TB_008", "category": "material_constraint", "title": "Wall Thickness for Material Type", "description": "Different materials require different minimum wall thickness", "rule": "Steel: >= 1.0mm, Aluminum: >= 0.8mm, Plastic: >= 1.5mm", "severity": "info", "validation_code": "material_specific_thickness_check(material_type, thickness)", "error_message": "INFO: Consider material-specific thickness requirements.", "parameters": ["material_type", "thickness"]}, {"id": "TB_009", "category": "surface_finish", "title": "Fillet Radius Manufacturing", "description": "Fillet radius should be achievable with standard tooling", "rule": "Fillet radius >= 0.1mm and <= 5.0mm for standard tooling", "severity": "info", "validation_code": "0.1 <= outer_fillet_radius <= 5.0 and 0.1 <= inner_fillet_radius <= 5.0", "error_message": "INFO: Consider standard tooling capabilities for fillet radius.", "parameters": ["outer_fillet_radius", "inner_fillet_radius"]}, {"id": "TB_010", "category": "structural_integrity", "title": "Buckling Prevention", "description": "Wall thickness to prevent buckling under load", "rule": "For thin-walled tubes: thickness/outer_diameter >= 0.01", "severity": "warning", "validation_code": "thickness / max(outer_width, outer_height) >= 0.01", "error_message": "WARNING: Tube may be susceptible to buckling under load.", "parameters": ["thickness", "outer_width", "outer_height"]}]}